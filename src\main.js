// Import Pico CSS
import '@picocss/pico/css/pico.min.css';
import './styles/main.css';

// Import your custom styles if needed
// import './styles/main.scss';

// function to replcace maps.google.com with maps.apple.com when iphone is detected
function replaceGoogleMapsLinks() {
  // Check if user is on iOS
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  
  if (isIOS) {
    // Get all links
    const links = document.getElementsByTagName('a');
    
    // Loop through links and replace Google Maps URLs with Apple Maps
    for (let link of links) {
      if (link.href.includes('maps.google.com')) {
        link.href = link.href.replace('maps.google.com', 'maps.apple.com');
      }
    }
  }
}

// Call the function when DOM is loaded
document.addEventListener('DOMContentLoaded', replaceGoogleMapsLinks);


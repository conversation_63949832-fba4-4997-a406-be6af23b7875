@import url("https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap");
body {
  background-color: rgb(130, 171, 0);
  margin: 0;
  padding: 20px;
  font-family: Arial, sans-serif;
  line-height: 1.6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #4a4a4a;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 2rem;
  margin-top: 0;
}

h2 {
  color: #4a4a4a;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  margin-top: 2rem;
}

h3 {
  color: #4a4a4a;
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  margin-top: 1.5rem;
}

.content-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

p {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1rem;
}

address a {
  text-decoration: none;
}

a[href^=tel] {
  text-decoration: none;
}

a {
  color: #0066cc;
  text-decoration: underline;
}
a:hover {
  color: #0052a3;
}

.signature {
  font-family: "Dancing Script", cursive;
  font-style: italic;
  font-size: 1.2rem;
  margin-top: 1rem;
}

.address {
  font-weight: bold;
  margin: 1rem 0;
}

ul {
  margin: 1rem 0;
  padding-left: 2rem;
}
ul li {
  margin-bottom: 0.5rem;
  color: #333;
}

@media (max-width: 768px) {
  body {
    padding: 10px;
  }
  h1 {
    font-size: 2rem;
  }
  h2 {
    font-size: 1.5rem;
  }
  .content-card {
    padding: 1rem;
  }
}/*# sourceMappingURL=main.css.map */